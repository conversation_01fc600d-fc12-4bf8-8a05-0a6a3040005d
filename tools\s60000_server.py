#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3

from mcp.server.fastmcp import FastMCP
import logging
import json
import requests
from typing import Dict, Any

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("S60000业务流服务")


@mcp.tool()
def execute_custom_task(
    custom_tasks: str
) -> Dict[str, Any]:
    """执行待处理任务查询操作

    Args:
        custom_tasks: 查询类型参数（JSON字符串格式的查询列表），如：'["工作通知", "工作任务"]'
                     支持的查询类型：工作通知、工作任务、工作联系单、预警单管理、地市预警通告

    Returns:
        标准化返回格式：{"status": "success/error", "data": ..., "message": "..."}
    """
    # 固定配置参数
    server_host = "*************"
    server_port = 6000
    timeout = 300.0

    logger.info(f"[S60000] 执行业务查询，服务器: {server_host}:{server_port}")

    try:

        # 解析自定义任务数据
        try:
            tasks_list = json.loads(custom_tasks)
            if not isinstance(tasks_list, list):
                return {
                    "status": "error",
                    "message": "查询参数必须是数组格式，如：[\"工作通知\", \"工作任务\"]"
                }
        except json.JSONDecodeError as e:
            logger.error(f"[S60000] 查询参数JSON格式错误: {e}")
            return {
                "status": "error",
                "message": f"查询参数JSON格式错误: {str(e)}"
            }

        # 构建请求URL（固定为/start-task接口）
        url = f"http://{server_host}:{server_port}/start-task"

        # 构建请求数据
        request_data = {
            "tasks": tasks_list
        }

        logger.info(f"[S60000] 执行业务查询: {tasks_list}")

        # 发送POST请求到/start-task接口
        response = requests.post(
            url,
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=timeout
        )

        # 检查响应状态
        if response.status_code == 200:
            result_data = response.json()
            logger.info("[S60000] 业务查询执行成功")
            return {
                "status": "success",
                "data": result_data,
                "message": "业务查询执行成功"
            }
        else:
            error_text = response.text
            logger.error(f"[S60000] 业务查询执行失败，状态码: {response.status_code}, 错误信息: {error_text}")
            return {
                "status": "error",
                "message": f"业务查询执行失败，状态码: {response.status_code}，错误信息: {error_text}"
            }

    except requests.exceptions.Timeout:
        logger.error("[S60000] 请求超时")
        return {
            "status": "error",
            "message": "请求超时，请检查网络连接或增加超时时间"
        }
    except requests.exceptions.ConnectionError as e:
        logger.error(f"[S60000] 网络连接错误: {e}")
        return {
            "status": "error",
            "message": f"网络连接错误: {str(e)}"
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"[S60000] 请求错误: {e}")
        return {
            "status": "error",
            "message": f"请求错误: {str(e)}"
        }
    except Exception as e:
        logger.error(f"[S60000] 未知错误: {e}")
        return {
            "status": "error",
            "message": f"执行过程中发生未知错误: {str(e)}"
        }


@mcp.tool()
def check_deadlines() -> Dict[str, Any]:
    """执行反馈时间截止查询，检查各类业务的反馈截止时间

    Returns:
        标准化返回格式：{"status": "success/error", "data": ..., "message": "..."}
    """
    # 固定配置参数
    server_host = "*************"
    server_port = 6000
    timeout = 300.0

    logger.info(f"[S60000] 执行反馈时间截止查询，服务器: {server_host}:{server_port}")

    try:

        # 构建请求URL
        url = f"http://{server_host}:{server_port}/check-deadlines"

        # 发送GET请求
        response = requests.get(url, timeout=timeout)

        # 检查响应状态
        if response.status_code == 200:
            result_data = response.json()
            logger.info("[S60000] 反馈时间截止查询执行成功")
            return {
                "status": "success",
                "data": result_data,
                "message": "反馈时间截止查询执行成功"
            }
        else:
            error_text = response.text
            logger.error(f"[S60000] 反馈时间截止查询执行失败，状态码: {response.status_code}, 错误信息: {error_text}")
            return {
                "status": "error",
                "message": f"反馈时间截止查询执行失败，状态码: {response.status_code}，错误信息: {error_text}"
            }

    except requests.exceptions.Timeout:
        logger.error("[S60000] 请求超时")
        return {
            "status": "error",
            "message": "请求超时，请检查网络连接或增加超时时间"
        }
    except requests.exceptions.ConnectionError as e:
        logger.error(f"[S60000] 网络连接错误: {e}")
        return {
            "status": "error",
            "message": f"网络连接错误: {str(e)}"
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"[S60000] 请求错误: {e}")
        return {
            "status": "error",
            "message": f"请求错误: {str(e)}"
        }
    except Exception as e:
        logger.error(f"[S60000] 未知错误: {e}")
        return {
            "status": "error",
            "message": f"执行过程中发生未知错误: {str(e)}"
        }


if __name__ == "__main__":
    mcp.run()