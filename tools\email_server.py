from mcp.server.fastmcp import FastMCP
import logging
from typing import List, Dict

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("邮件服务")

@mcp.tool()
def check_new_mail() -> List[Dict[str, str]]:
    """收取到新邮件时，发起通知。此工具用于检查邮箱并返回新收到的未读邮件列表。
    
    Returns:
        新邮件列表，每个元素包含from、subject、received_at、snippet字段
    """
    logger.info("调用check_new_mail方法，检查新邮件")
    
    # 返回固定的测试邮件数据
    return [
        {
            "from": "<EMAIL>",
            "subject": "紧急：发现高危安全漏洞",
            "received_at": "2024-01-15 16:30:00",
            "snippet": "在系统扫描中发现CVE-2024-0001高危漏洞，请立即处理..."
        },
        {
            "from": "<EMAIL>", 
            "subject": "网络设备维护通知",
            "received_at": "2024-01-15 15:45:00",
            "snippet": "计划于今晚22:00-02:00进行核心交换机维护，期间可能影响..."
        },
        {
            "from": "<EMAIL>",
            "subject": "系统监控告警报告",
            "received_at": "2024-01-15 14:20:00", 
            "snippet": "检测到异常登录行为，来源IP：*************，请核实..."
        },
        {
            "from": "<EMAIL>",
            "subject": "数据备份完成通知",
            "received_at": "2024-01-15 13:15:00",
            "snippet": "今日数据备份已成功完成，备份大小：2.5GB，存储位置..."
        },
        {
            "from": "<EMAIL>",
            "subject": "安全策略更新提醒",
            "received_at": "2024-01-15 12:00:00",
            "snippet": "新版安全策略已发布，请及时更新本地配置文件..."
        }
    ]

if __name__ == "__main__":
    logger.info("启动邮件服务MCP服务器")
    mcp.run(transport="stdio")
