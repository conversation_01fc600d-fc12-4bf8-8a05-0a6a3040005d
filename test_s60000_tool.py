#!/usr/bin/env python
# encoding:utf-8

"""
测试 S60000 工具的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.s60000_server import execute_custom_task, check_deadlines


def test_execute_custom_task():
    """测试执行业务查询功能"""
    print("=" * 50)
    print("测试执行业务查询功能")
    print("=" * 50)

    # 测试参数（服务器配置已固定为192.168.40.32:6000）
    custom_tasks = '["任务查询", "通知查询"]'

    print(f"服务器: 192.168.40.32:6000 (固定配置)")
    print(f"业务查询: {custom_tasks}")
    print(f"超时时间: 300秒 (固定配置)")
    print()

    try:
        result = execute_custom_task(custom_tasks)
        print("执行结果:")
        print(f"状态: {result['status']}")
        if result['status'] == 'success':
            print(f"数据: {result.get('data', 'N/A')}")
        print(f"消息: {result['message']}")
    except Exception as e:
        print(f"执行失败: {e}")

    print()


def test_check_deadlines():
    """测试反馈时间截止查询功能"""
    print("=" * 50)
    print("测试反馈时间截止查询功能")
    print("=" * 50)

    print(f"服务器: 192.168.40.32:6000 (固定配置)")
    print(f"超时时间: 300秒 (固定配置)")
    print()

    try:
        result = check_deadlines()
        print("执行结果:")
        print(f"状态: {result['status']}")
        if result['status'] == 'success':
            print(f"数据: {result.get('data', 'N/A')}")
        print(f"消息: {result['message']}")
    except Exception as e:
        print(f"执行失败: {e}")

    print()


def test_parameter_validation():
    """测试参数验证功能"""
    print("=" * 50)
    print("测试参数验证功能")
    print("=" * 50)

    # 测试无效JSON格式
    print("1. 测试无效JSON格式:")
    result = execute_custom_task('invalid json')
    print(f"   结果: {result['status']} - {result['message']}")

    # 测试非数组格式
    print("2. 测试非数组格式:")
    result = execute_custom_task('{"tasks": ["任务查询"]}')
    print(f"   结果: {result['status']} - {result['message']}")

    # 测试有效格式
    print("3. 测试有效格式:")
    result = execute_custom_task('["任务查询"]')
    print(f"   结果: {result['status']} - {result['message']}")

    print()


if __name__ == "__main__":
    print("S60000 工具测试")
    print("注意: 这些测试会尝试连接到指定的服务器，如果服务器不可达，会显示连接错误，这是正常的。")
    print()
    
    test_parameter_validation()
    test_execute_custom_task()
    test_check_deadlines()
    
    print("测试完成！")
