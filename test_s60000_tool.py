#!/usr/bin/env python
# encoding:utf-8

"""
测试 S60000 工具的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.s60000_server import execute_custom_task, check_deadlines


def test_execute_custom_task():
    """测试执行自定义任务功能"""
    print("=" * 50)
    print("测试执行自定义任务功能")
    print("=" * 50)
    
    # 测试参数
    server_host = "*************"
    server_port = 8080
    custom_tasks = '["工作任务", "预警单管理"]'
    timeout = 30.0
    
    print(f"服务器: {server_host}:{server_port}")
    print(f"自定义任务: {custom_tasks}")
    print(f"超时时间: {timeout}秒")
    print()
    
    try:
        result = execute_custom_task(server_host, server_port, custom_tasks, timeout)
        print("执行结果:")
        print(f"状态: {result['status']}")
        if result['status'] == 'success':
            print(f"数据: {result.get('data', 'N/A')}")
        print(f"消息: {result['message']}")
    except Exception as e:
        print(f"执行失败: {e}")
    
    print()


def test_check_deadlines():
    """测试截止时间检测功能"""
    print("=" * 50)
    print("测试截止时间检测功能")
    print("=" * 50)
    
    # 测试参数
    server_host = "*************"
    server_port = 8080
    timeout = 30.0
    
    print(f"服务器: {server_host}:{server_port}")
    print(f"超时时间: {timeout}秒")
    print()
    
    try:
        result = check_deadlines(server_host, server_port, timeout)
        print("执行结果:")
        print(f"状态: {result['status']}")
        if result['status'] == 'success':
            print(f"数据: {result.get('data', 'N/A')}")
        print(f"消息: {result['message']}")
    except Exception as e:
        print(f"执行失败: {e}")
    
    print()


def test_parameter_validation():
    """测试参数验证功能"""
    print("=" * 50)
    print("测试参数验证功能")
    print("=" * 50)
    
    # 测试空服务器地址
    print("1. 测试空服务器地址:")
    result = execute_custom_task("", 8080, '["工作任务"]')
    print(f"   结果: {result['status']} - {result['message']}")
    
    # 测试无效端口
    print("2. 测试无效端口:")
    result = execute_custom_task("*************", -1, '["工作任务"]')
    print(f"   结果: {result['status']} - {result['message']}")
    
    # 测试无效JSON格式
    print("3. 测试无效JSON格式:")
    result = execute_custom_task("*************", 8080, 'invalid json')
    print(f"   结果: {result['status']} - {result['message']}")
    
    # 测试非数组格式
    print("4. 测试非数组格式:")
    result = execute_custom_task("*************", 8080, '{"tasks": ["工作任务"]}')
    print(f"   结果: {result['status']} - {result['message']}")
    
    print()


if __name__ == "__main__":
    print("S60000 工具测试")
    print("注意: 这些测试会尝试连接到指定的服务器，如果服务器不可达，会显示连接错误，这是正常的。")
    print()
    
    test_parameter_validation()
    test_execute_custom_task()
    test_check_deadlines()
    
    print("测试完成！")
