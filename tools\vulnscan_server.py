from mcp.server.fastmcp import FastMCP
import logging
from typing import List, Dict

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("漏洞扫描服务")

@mcp.tool()
def list_tasks() -> List[Dict[str, str]]:
    """查询漏扫系统的所有任务及其当前状态。
    
    Returns:
        任务列表，每个元素包含task_id、name、status、created_at字段
    """
    logger.info("调用list_tasks方法，查询漏洞扫描任务")
    
    # 返回固定的测试任务数据
    return [
        {
            "task_id": "SCAN001",
            "name": "Web应用安全扫描",
            "status": "运行中",
            "created_at": "2024-01-15 09:00:00"
        },
        {
            "task_id": "SCAN002",
            "name": "网络端口扫描",
            "status": "已完成",
            "created_at": "2024-01-15 08:30:00"
        },
        {
            "task_id": "SCAN003",
            "name": "主机漏洞扫描",
            "status": "等待中",
            "created_at": "2024-01-15 10:15:00"
        },
        {
            "task_id": "SCAN004",
            "name": "数据库安全扫描",
            "status": "已完成",
            "created_at": "2024-01-14 16:45:00"
        },
        {
            "task_id": "SCAN005",
            "name": "SSL证书检查",
            "status": "运行中",
            "created_at": "2024-01-15 11:20:00"
        },
        {
            "task_id": "SCAN006",
            "name": "恶意软件扫描",
            "status": "失败",
            "created_at": "2024-01-15 07:30:00"
        },
        {
            "task_id": "SCAN007",
            "name": "配置合规性检查",
            "status": "已完成",
            "created_at": "2024-01-14 14:20:00"
        },
        {
            "task_id": "SCAN008",
            "name": "API安全测试",
            "status": "等待中",
            "created_at": "2024-01-15 12:00:00"
        }
    ]

if __name__ == "__main__":
    logger.info("启动漏洞扫描服务MCP服务器")
    mcp.run(transport="stdio")
