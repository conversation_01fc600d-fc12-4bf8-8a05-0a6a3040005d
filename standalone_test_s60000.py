#!/usr/bin/env python
# encoding:utf-8

"""
S60000 工具独立测试脚本
不依赖 MCP 框架，直接测试工具的核心功能
"""

import json
import requests
import logging
from typing import Dict, Any

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def execute_custom_task_standalone(custom_tasks: str) -> Dict[str, Any]:
    """独立版本：执行待处理查询、任务查询、通知查询等业务查询操作
    
    Args:
        custom_tasks: 查询类型参数（JSON字符串格式的查询列表），如：'["任务查询", "通知查询"]'
    
    Returns:
        标准化返回格式：{"status": "success/error", "data": ..., "message": "..."}
    """
    # 固定配置参数
    server_host = "*************"
    server_port = 6000
    timeout = 300.0
    
    logger.info(f"[S60000] 执行业务查询，服务器: {server_host}:{server_port}")
    
    try:
        # 解析查询参数数据
        try:
            tasks_list = json.loads(custom_tasks)
            if not isinstance(tasks_list, list):
                return {
                    "status": "error",
                    "message": "查询参数必须是数组格式，如：[\"任务查询\", \"通知查询\"]"
                }
        except json.JSONDecodeError as e:
            logger.error(f"[S60000] 查询参数JSON格式错误: {e}")
            return {
                "status": "error",
                "message": f"查询参数JSON格式错误: {str(e)}"
            }

        # 构建请求URL（固定为/start-task接口）
        url = f"http://{server_host}:{server_port}/start-task"

        # 构建请求数据
        request_data = {
            "tasks": tasks_list
        }

        logger.info(f"[S60000] 执行业务查询: {tasks_list}")

        # 发送POST请求到/start-task接口
        response = requests.post(
            url,
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=timeout
        )

        # 检查响应状态
        if response.status_code == 200:
            result_data = response.json()
            logger.info("[S60000] 业务查询执行成功")
            return {
                "status": "success",
                "data": result_data,
                "message": "业务查询执行成功"
            }
        else:
            error_text = response.text
            logger.error(f"[S60000] 业务查询执行失败，状态码: {response.status_code}, 错误信息: {error_text}")
            return {
                "status": "error",
                "message": f"业务查询执行失败，状态码: {response.status_code}，错误信息: {error_text}"
            }

    except requests.exceptions.Timeout:
        logger.error("[S60000] 请求超时")
        return {
            "status": "error",
            "message": "请求超时，请检查网络连接或增加超时时间"
        }
    except requests.exceptions.ConnectionError as e:
        logger.error(f"[S60000] 网络连接错误: {e}")
        return {
            "status": "error",
            "message": f"网络连接错误: {str(e)}"
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"[S60000] 请求错误: {e}")
        return {
            "status": "error",
            "message": f"请求错误: {str(e)}"
        }
    except Exception as e:
        logger.error(f"[S60000] 未知错误: {e}")
        return {
            "status": "error",
            "message": f"执行过程中发生未知错误: {str(e)}"
        }


def check_deadlines_standalone() -> Dict[str, Any]:
    """独立版本：执行反馈时间截止查询，检查各类业务的反馈截止时间
    
    Returns:
        标准化返回格式：{"status": "success/error", "data": ..., "message": "..."}
    """
    # 固定配置参数
    server_host = "*************"
    server_port = 6000
    timeout = 300.0
    
    logger.info(f"[S60000] 执行反馈时间截止查询，服务器: {server_host}:{server_port}")
    
    try:
        # 构建请求URL
        url = f"http://{server_host}:{server_port}/check-deadlines"

        # 发送GET请求
        response = requests.get(url, timeout=timeout)

        # 检查响应状态
        if response.status_code == 200:
            result_data = response.json()
            logger.info("[S60000] 反馈时间截止查询执行成功")
            return {
                "status": "success",
                "data": result_data,
                "message": "反馈时间截止查询执行成功"
            }
        else:
            error_text = response.text
            logger.error(f"[S60000] 反馈时间截止查询执行失败，状态码: {response.status_code}, 错误信息: {error_text}")
            return {
                "status": "error",
                "message": f"反馈时间截止查询执行失败，状态码: {response.status_code}，错误信息: {error_text}"
            }

    except requests.exceptions.Timeout:
        logger.error("[S60000] 请求超时")
        return {
            "status": "error",
            "message": "请求超时，请检查网络连接或增加超时时间"
        }
    except requests.exceptions.ConnectionError as e:
        logger.error(f"[S60000] 网络连接错误: {e}")
        return {
            "status": "error",
            "message": f"网络连接错误: {str(e)}"
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"[S60000] 请求错误: {e}")
        return {
            "status": "error",
            "message": f"请求错误: {str(e)}"
        }
    except Exception as e:
        logger.error(f"[S60000] 未知错误: {e}")
        return {
            "status": "error",
            "message": f"执行过程中发生未知错误: {str(e)}"
        }


def test_business_queries():
    """测试业务查询功能"""
    print("=" * 60)
    print("测试业务查询功能")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "任务查询",
            "query": '["任务查询"]'
        },
        {
            "name": "通知查询",
            "query": '["通知查询"]'
        },
        {
            "name": "待处理查询",
            "query": '["待处理查询"]'
        },
        {
            "name": "联系单查询",
            "query": '["联系单查询"]'
        },
        {
            "name": "反馈查询",
            "query": '["反馈查询"]'
        },
        {
            "name": "多项查询",
            "query": '["任务查询", "通知查询", "待处理查询"]'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}:")
        print(f"   查询参数: {test_case['query']}")
        
        try:
            result = execute_custom_task_standalone(test_case['query'])
            print(f"   状态: {result['status']}")
            print(f"   消息: {result['message']}")
            if result['status'] == 'success' and result.get('data'):
                print(f"   数据: {result['data']}")
        except Exception as e:
            print(f"   执行失败: {e}")
        
        print("-" * 40)


def test_deadline_query():
    """测试反馈时间截止查询功能"""
    print("=" * 60)
    print("测试反馈时间截止查询功能")
    print("=" * 60)
    
    print("执行反馈时间截止查询...")
    
    try:
        result = check_deadlines_standalone()
        print(f"状态: {result['status']}")
        print(f"消息: {result['message']}")
        if result['status'] == 'success' and result.get('data'):
            print(f"数据: {result['data']}")
    except Exception as e:
        print(f"执行失败: {e}")


def test_parameter_validation():
    """测试参数验证功能"""
    print("=" * 60)
    print("测试参数验证功能")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "无效JSON格式",
            "query": "invalid json"
        },
        {
            "name": "非数组格式",
            "query": '{"tasks": ["任务查询"]}'
        },
        {
            "name": "空数组",
            "query": '[]'
        },
        {
            "name": "有效格式",
            "query": '["任务查询"]'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}:")
        print(f"   输入: {test_case['query']}")
        
        try:
            result = execute_custom_task_standalone(test_case['query'])
            print(f"   状态: {result['status']}")
            print(f"   消息: {result['message']}")
        except Exception as e:
            print(f"   执行失败: {e}")


def main():
    """主测试函数"""
    print("S60000 工具独立测试")
    print("服务器配置: *************:6000")
    print("超时时间: 300秒")
    print("注意: 如果服务器不可达，会显示连接错误，这是正常的。")
    print()
    
    # 运行所有测试
    test_parameter_validation()
    print("\n")
    test_business_queries()
    print("\n")
    test_deadline_query()
    
    print("\n" + "=" * 60)
    print("测试完成！")


if __name__ == "__main__":
    main()
