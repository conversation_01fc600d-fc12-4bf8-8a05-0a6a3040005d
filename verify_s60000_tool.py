#!/usr/bin/env python
# encoding:utf-8

"""
验证 S60000 工具的基本功能
"""

def test_import():
    """测试导入功能"""
    try:
        from tools.s60000_server import execute_custom_task, check_deadlines
        print("✅ 工具导入成功")
        return True
    except ImportError as e:
        print(f"❌ 工具导入失败: {e}")
        return False

def test_parameter_validation():
    """测试参数验证"""
    try:
        from tools.s60000_server import execute_custom_task, check_deadlines
        
        print("\n测试参数验证:")
        
        # 测试空服务器地址
        result = execute_custom_task("", 8080, '["工作任务"]')
        if result['status'] == 'error' and '服务器地址不能为空' in result['message']:
            print("✅ 空服务器地址验证通过")
        else:
            print("❌ 空服务器地址验证失败")
        
        # 测试无效端口
        result = execute_custom_task("192.168.1.100", -1, '["工作任务"]')
        if result['status'] == 'error' and '端口必须是正整数' in result['message']:
            print("✅ 无效端口验证通过")
        else:
            print("❌ 无效端口验证失败")
        
        # 测试无效JSON
        result = execute_custom_task("192.168.1.100", 8080, 'invalid json')
        if result['status'] == 'error' and 'JSON格式错误' in result['message']:
            print("✅ 无效JSON验证通过")
        else:
            print("❌ 无效JSON验证失败")
        
        return True
    except Exception as e:
        print(f"❌ 参数验证测试失败: {e}")
        return False

def test_function_signatures():
    """测试函数签名"""
    try:
        from tools.s60000_server import execute_custom_task, check_deadlines
        import inspect
        
        print("\n测试函数签名:")
        
        # 检查 execute_custom_task 签名
        sig = inspect.signature(execute_custom_task)
        params = list(sig.parameters.keys())
        expected_params = ['server_host', 'server_port', 'custom_tasks', 'timeout']
        
        if params == expected_params:
            print("✅ execute_custom_task 函数签名正确")
        else:
            print(f"❌ execute_custom_task 函数签名错误: {params}")
        
        # 检查 check_deadlines 签名
        sig = inspect.signature(check_deadlines)
        params = list(sig.parameters.keys())
        expected_params = ['server_host', 'server_port', 'timeout']
        
        if params == expected_params:
            print("✅ check_deadlines 函数签名正确")
        else:
            print(f"❌ check_deadlines 函数签名错误: {params}")
        
        return True
    except Exception as e:
        print(f"❌ 函数签名测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("S60000 工具验证")
    print("=" * 40)
    
    success_count = 0
    total_tests = 3
    
    # 测试导入
    if test_import():
        success_count += 1
    
    # 测试参数验证
    if test_parameter_validation():
        success_count += 1
    
    # 测试函数签名
    if test_function_signatures():
        success_count += 1
    
    print(f"\n验证结果: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！S60000 工具已准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查工具实现。")

if __name__ == "__main__":
    main()
