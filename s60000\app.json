{"identification": "w5soar", "is_public": true, "name": "S60000自动化", "version": "2.0", "description": "S60000系统自动化任务执行工具，支持五个任务模块查询", "type": "自动化执行", "action": [{"name": "未处理任务查询", "func": "execute_tasks"}, {"name": "反馈截止检测", "func": "check_deadlines"}, {"name": "自定义查询任务", "func": "execute_custom"}], "args": {"execute_tasks": [{"key": "server_host", "type": "text", "required": true, "default": "*************", "description": "Windows代理服务器IP地址"}, {"key": "server_port", "type": "number", "required": true, "default": 6000, "description": "Windows代理服务器端口"}, {"key": "timeout", "type": "number", "required": false, "default": 300, "description": "请求超时时间（秒）"}], "check_deadlines": [{"key": "server_host", "type": "text", "required": true, "default": "*************", "description": "Windows代理服务器IP地址"}, {"key": "server_port", "type": "number", "required": true, "default": 6000, "description": "Windows代理服务器端口"}, {"key": "timeout", "type": "number", "required": false, "default": 300, "description": "请求超时时间（秒）"}], "execute_custom": [{"key": "server_host", "type": "text", "required": true, "default": "*************", "description": "Windows代理服务器IP地址"}, {"key": "server_port", "type": "number", "required": true, "default": 6000, "description": "Windows代理服务器端口"}, {"key": "custom_tasks", "type": "textarea", "required": true, "default": "[\"工作通知\", \"工作任务\", \"工作联系单\", \"预警单管理\", \"地市预警通告\"]", "description": "自定义任务参数（JSON格式，如：[\"工作任务\", \"预警单管理\"]）"}, {"key": "timeout", "type": "number", "required": false, "default": 300, "description": "请求超时时间（秒）"}]}}