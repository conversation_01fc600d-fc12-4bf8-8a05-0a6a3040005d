#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3

import json
import aiohttp
import asyncio
from loguru import logger


async def execute_tasks(server_host, server_port, timeout=300):
    """
    执行指定的S60000模块任务（包含所有五个模块）

    Args:
        server_host: Windows代理服务器IP地址
        server_port: Windows代理服务器端口
        timeout: 请求超时时间（秒）

    Returns:
        dict: 执行结果
    """
    # 参数类型转换
    try:
        server_port = int(server_port)
        timeout = float(timeout)
    except (ValueError, TypeError) as e:
        logger.error("[S60000] 参数类型错误：{error}", error=str(e))
        return {
            "status": 2,
            "result": f"参数类型错误：{str(e)}"
        }

    # 固定的五个任务模块
    all_tasks = ["工作通知", "工作任务", "工作联系单", "预警单管理", "地市预警通告"]

    logger.info("[S60000] 执行指定模块任务（全部五个模块）: {tasks}", tasks=all_tasks)

    # 构建请求URL
    url = f"http://{server_host}:{server_port}/start-task"

    # 构建请求数据
    request_data = {
        "tasks": all_tasks
    }
    
    try:
        # 创建HTTP客户端会话
        timeout_config = aiohttp.ClientTimeout(total=timeout)
        async with aiohttp.ClientSession(timeout=timeout_config) as session:
            # 发送POST请求
            async with session.post(
                url,
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                
                # 检查响应状态
                if response.status == 200:
                    result_data = await response.json()
                    logger.info("[S60000] 任务执行成功")
                    return {
                        "status": 0,
                        "result": result_data
                    }
                else:
                    error_text = await response.text()
                    logger.error("[S60000] 任务执行失败，状态码: {status}, 错误信息: {error}",
                               status=response.status, error=error_text)
                    return {
                        "status": 1,
                        "result": f"任务执行失败，状态码: {response.status}，错误信息: {error_text}"
                    }
                    
    except asyncio.TimeoutError:
        logger.error("[S60000] 请求超时")
        return {
            "status": 2,
            "result": "请求超时，请检查网络连接或增加超时时间"
        }
    except aiohttp.ClientError as e:
        logger.error("[S60000] 网络连接错误: {error}", error=str(e))
        return {
            "status": 2,
            "result": f"网络连接错误: {str(e)}"
        }
    except Exception as e:
        logger.error("[S60000] 未知错误: {error}", error=str(e))
        return {
            "status": 2,
            "result": f"执行过程中发生未知错误: {str(e)}"
        }


async def check_deadlines(server_host, server_port, timeout=300):
    """
    执行截止时间检测任务
    
    Args:
        server_host: Windows代理服务器IP地址
        server_port: Windows代理服务器端口
        timeout: 请求超时时间（秒）
    
    Returns:
        dict: 执行结果
    """
    logger.info("[S60000] 执行截止时间检测任务")
    
    # 参数类型转换
    try:
        server_port = int(server_port)
        timeout = float(timeout)
    except (ValueError, TypeError) as e:
        logger.error("[S60000] 参数类型错误：{error}", error=str(e))
        return {
            "status": 2,
            "result": f"参数类型错误：{str(e)}"
        }

    # 构建请求URL
    url = f"http://{server_host}:{server_port}/check-deadlines"
    
    try:
        # 创建HTTP客户端会话
        timeout_config = aiohttp.ClientTimeout(total=timeout)
        async with aiohttp.ClientSession(timeout=timeout_config) as session:
            # 发送GET请求
            async with session.get(url) as response:
                
                # 检查响应状态
                if response.status == 200:
                    result_data = await response.json()
                    logger.info("[S60000] 截止时间检测任务执行成功")
                    return {
                        "status": 0,
                        "result": result_data
                    }
                else:
                    error_text = await response.text()
                    logger.error("[S60000] 截止时间检测任务执行失败，状态码: {status}, 错误信息: {error}",
                               status=response.status, error=error_text)
                    return {
                        "status": 1,
                        "result": f"截止时间检测任务执行失败，状态码: {response.status}，错误信息: {error_text}"
                    }
                    
    except asyncio.TimeoutError:
        logger.error("[S60000] 请求超时")
        return {
            "status": 2,
            "result": "请求超时，请检查网络连接或增加超时时间"
        }
    except aiohttp.ClientError as e:
        logger.error("[S60000] 网络连接错误: {error}", error=str(e))
        return {
            "status": 2,
            "result": f"网络连接错误: {str(e)}"
        }
    except Exception as e:
        logger.error("[S60000] 未知错误: {error}", error=str(e))
        return {
            "status": 2,
            "result": f"执行过程中发生未知错误: {str(e)}"
        }


async def execute_custom(server_host, server_port, custom_tasks, timeout=300):
    """
    执行自定义任务（仅限/start-task接口）

    Args:
        server_host: Windows代理服务器IP地址
        server_port: Windows代理服务器端口
        custom_tasks: 自定义任务参数（JSON字符串格式的任务列表）
        timeout: 请求超时时间（秒）

    Returns:
        dict: 执行结果
    """
    logger.info("[S60000] 执行自定义任务")

    # 参数类型转换
    try:
        server_port = int(server_port)
        timeout = float(timeout)
    except (ValueError, TypeError) as e:
        logger.error("[S60000] 参数类型错误：{error}", error=str(e))
        return {
            "status": 2,
            "result": f"参数类型错误：{str(e)}"
        }

    # 构建请求URL（固定为/start-task接口）
    url = f"http://{server_host}:{server_port}/start-task"

    # 解析自定义任务数据
    try:
        tasks_list = json.loads(custom_tasks)
        if not isinstance(tasks_list, list):
            return {
                "status": 2,
                "result": "自定义任务参数必须是数组格式，如：[\"工作任务\", \"预警单管理\"]"
            }
    except json.JSONDecodeError as e:
        logger.error("[S60000] 自定义任务参数JSON格式错误: {error}", error=str(e))
        return {
            "status": 2,
            "result": f"自定义任务参数JSON格式错误: {str(e)}"
        }

    # 构建请求数据
    request_data = {
        "tasks": tasks_list
    }

    logger.info("[S60000] 执行自定义任务: {tasks}", tasks=tasks_list)

    try:
        # 创建HTTP客户端会话
        timeout_config = aiohttp.ClientTimeout(total=timeout)
        async with aiohttp.ClientSession(timeout=timeout_config) as session:
            # 发送POST请求到/start-task接口
            async with session.post(
                url,
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:

                # 检查响应状态
                if response.status == 200:
                    result_data = await response.json()
                    logger.info("[S60000] 自定义任务执行成功")
                    return {
                        "status": 0,
                        "result": result_data
                    }
                else:
                    error_text = await response.text()
                    logger.error("[S60000] 自定义任务执行失败，状态码: {status}, 错误信息: {error}",
                               status=response.status, error=error_text)
                    return {
                        "status": 1,
                        "result": f"自定义任务执行失败，状态码: {response.status}，错误信息: {error_text}"
                    }

    except asyncio.TimeoutError:
        logger.error("[S60000] 请求超时")
        return {
            "status": 2,
            "result": "请求超时，请检查网络连接或增加超时时间"
        }
    except aiohttp.ClientError as e:
        logger.error("[S60000] 网络连接错误: {error}", error=str(e))
        return {
            "status": 2,
            "result": f"网络连接错误: {str(e)}"
        }
    except Exception as e:
        logger.error("[S60000] 未知错误: {error}", error=str(e))
        return {
            "status": 2,
            "result": f"执行过程中发生未知错误: {str(e)}"
        }
