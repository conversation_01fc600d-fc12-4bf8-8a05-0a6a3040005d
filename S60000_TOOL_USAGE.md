# S60000 工具使用说明

## 概述

S60000 工具已成功从 `s60000/main/run.py` 迁移到 MCP 工具系统中，位于 `tools/s60000_server.py`。该工具提供了两个主要功能：

1. **业务查询功能** (`execute_custom_task`) - 支持待处理查询、任务查询、通知查询等
2. **反馈时间截止查询** (`check_deadlines`) - 检查各类业务的反馈截止时间

## 功能详情

### 1. 业务查询功能 (execute_custom_task)

**功能描述**: 向固定的 Windows 代理服务器发送业务查询请求，通过 `/start-task` 接口执行各类业务查询操作。

**固定配置**:
- 服务器地址: `192.168.40.32`
- 服务器端口: `6000`
- 超时时间: `300秒`

**参数**:
- `custom_tasks` (str): 查询类型参数（JSON字符串格式的查询列表）

**使用示例**:
```python
# 通过对话接口调用
"请执行S60000业务查询，查询类型为任务查询和通知查询"

# 对应的参数
custom_tasks = '["任务查询", "通知查询"]'
```

**支持的查询类型**:
- "待处理查询" - 查询待处理的业务事项
- "任务查询" - 查询工作任务相关信息
- "通知查询" - 查询工作通知相关信息
- "联系单查询" - 查询工作联系单相关信息
- "反馈查询" - 查询预警单反馈相关信息

### 2. 反馈时间截止查询 (check_deadlines)

**功能描述**: 向固定的 Windows 代理服务器发送反馈时间截止查询请求，通过 `/check-deadlines` 接口检查各类业务的反馈截止时间。

**固定配置**:
- 服务器地址: `192.168.40.32`
- 服务器端口: `6000`
- 超时时间: `300秒`

**参数**: 无

**使用示例**:
```python
# 通过对话接口调用
"请执行S60000反馈时间截止查询"
```

## 返回格式

两个功能都返回标准化的格式：

```json
{
    "status": "success|error",
    "data": "响应数据（仅成功时）",
    "message": "执行结果描述"
}
```

## 错误处理

工具包含完整的错误处理机制：

1. **JSON格式错误**: 查询参数格式不正确
2. **网络错误**: 连接超时、连接失败、请求异常等
3. **HTTP错误**: 服务器返回非200状态码

## 与原始功能的对比

### 原始 `s60000/main/run.py` 功能:
- `execute_tasks()`: 执行固定的五个模块任务
- `execute_custom()`: 执行自定义任务（仅限/start-task接口）
- `check_deadlines()`: 执行截止时间检测任务

### 迁移后的 MCP 工具功能:
- ✅ `execute_custom_task()`: 对应原始的 `execute_custom()` 功能，现改为业务查询功能
- ✅ `check_deadlines()`: 对应原始的 `check_deadlines()` 功能，现改为反馈时间截止查询
- ❌ `execute_tasks()`: 未迁移（固定五个模块的功能）

**注意**: 根据需求，只迁移了业务查询和反馈时间截止查询两个功能。功能说明已更新为更直观的业务术语。

## 使用方式

### 1. 通过主服务器对话接口

启动主服务器：
```bash
python main_server.py
```

发送对话请求：
```bash
curl -X POST "http://localhost:28123/chat" \
     -H "Content-Type: application/json" \
     -d '{"message": "请执行S60000业务查询，查询类型为任务查询"}'
```

### 2. 直接调用工具函数

```python
from tools.s60000_server import execute_custom_task, check_deadlines

# 执行业务查询
result = execute_custom_task('["任务查询", "通知查询"]')

# 执行反馈时间截止查询
result = check_deadlines()
```

## 日志记录

工具包含详细的日志记录，包括：
- 任务执行开始和结束
- 参数验证结果
- 网络请求状态
- 错误信息详情

日志格式：`%(asctime)s - %(levelname)s - %(message)s`

## 依赖要求

- `requests`: HTTP请求库
- `mcp.server.fastmcp`: MCP服务器框架
- `json`: JSON数据处理
- `logging`: 日志记录

## 测试

可以使用提供的测试脚本验证功能：

### 1. MCP框架测试
```bash
python test_s60000_tool.py
```

### 2. 独立测试（不依赖MCP）
```bash
python standalone_test_s60000.py
```

测试包括：
- 参数验证测试
- 业务查询功能测试
- 反馈时间截止查询测试
