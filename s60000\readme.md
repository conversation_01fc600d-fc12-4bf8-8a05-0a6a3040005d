# S60000自动化任务

## 应用简介

S60000自动化任务应用通过HTTP API接口与Windows代理服务器通信，触发S60000系统的自动化任务执行。

## 功能特性

### 1. 未处理任务查询
- 直接执行所有五个预定义的任务模块
- 包含的模块：
  - 工作通知
  - 工作任务
  - 工作联系单
  - 预警单管理
  - 地市预警通告
- 无需选择，一次性执行全部模块

### 2. 反馈截止检测
- 专门用于触发截止时间检测任务
- 无需额外参数，直接执行

### 3. 自定义查询任务
- 仅支持/start-task接口
- 可以自定义输入任务参数
- 支持JSON格式的任务列表输入

## 配置参数

### 通用参数
- **server_host**: Windows代理服务器IP地址（默认：*************）
- **server_port**: Windows代理服务器端口（默认：6000）
- **timeout**: 请求超时时间，单位秒（默认：300）

### 自定义任务参数
- **custom_tasks**: 自定义任务参数（JSON格式的任务列表），形如：["工作通知", "工作任务", "工作联系单", "预警单管理", "地市预警通告"]

## 使用示例

### 1. 执行指定模块任务（全部五个模块）
```json
{
  "server_host": "*************",
  "server_port": 6000,
  "timeout": 300
}
```

### 2. 执行截止时间检测
```json
{
  "server_host": "*************",
  "server_port": 6000,
  "timeout": 300
}
```

### 3. 执行自定义任务
```json
{
  "server_host": "*************",
  "server_port": 6000,
  "custom_tasks": "[\"工作任务\", \"预警单管理\"]",
  "timeout": 300
}
```

## 返回结果格式

### 成功响应
```json
{
  "status": 0,
  "result": {
    // S60000系统返回的具体数据（直接返回接口结果）
  }
}
```

### 失败响应
```json
{
  "status": 1,
  "result": "任务执行失败，状态码: 400，错误信息: 具体错误内容"
}
```

### 网络错误响应
```json
{
  "status": 2,
  "result": "网络连接错误: Connection refused"
}
```


## 注意事项

1. 确保Windows代理服务器（windows_agent.py）正在运行，目前运行在与服务器的windows虚拟机（*************）上
2. 确保网络连接正常，防火墙允许访问指定端口（默认6000）
3. 自定义数据必须是有效的JSON格式
4. 建议根据实际网络环境调整超时时间
5. 任务执行可能需要较长时间，请耐心等待
6. windows_agent.py处理请求会使用队列锁，同时只能执行一个S60000任务以避免冲突，后续请求需要持续等待或超时取消

## 错误处理

应用包含完善的错误处理机制：
- 网络连接错误
- 请求超时错误
- JSON格式错误
- HTTP状态码错误
- 其他未知错误

所有错误都会记录到日志中，并返回相应的错误信息给用户。
